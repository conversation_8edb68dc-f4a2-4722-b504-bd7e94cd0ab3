[server]
# Production server settings
headless = true
port = 8501
address = "0.0.0.0"

# Enable CORS with proper configuration for production
enableCORS = true
corsAllowedOrigins = []

# Enable XSRF protection for security
enableXsrfProtection = true

# File upload settings
maxUploadSize = 200
maxMessageSize = 200

# Session settings
disconnectedSessionTTL = 3600

# Enable websocket compression for better performance
enableWebsocketCompression = true

# Enable static file serving if needed
enableStaticServing = false

[browser]
# Browser configuration for production
gatherUsageStats = false
serverAddress = "localhost"

[client]
# Client configuration
showErrorDetails = false
showSidebarNavigation = true
toolbarMode = "minimal"

[theme]
# Optional: Set a consistent theme
primaryColor = "#FF6B6B"
backgroundColor = "#FFFFFF"
secondaryBackgroundColor = "#F0F2F6"
textColor = "#262730"
